package adhoc.event

import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositoryImpl
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import com.fasterxml.jackson.databind.ObjectMapper
import io.reactivex.Flowable
import java.time.Instant
import java.util.concurrent.atomic.AtomicBoolean
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthBlockNumber
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.TransactionReceipt
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.s3.S3Client
import spock.lang.Shared
import spock.lang.Specification

class MonitorEventServiceSocketSpec extends Specification {

	@Shared
	DynamoDbClient dynamoDbClient
	@Shared
	S3Client s3Client

	LoggingService mockLogger
	EventRepository eventRepo
	BlockHeightRepository blockHeightRepo
	EventLogRepository eventLogRepo
	BcmonitoringConfigurationProperties mockProperties
	BcmonitoringConfigurationProperties.Subscription mockSubscription
	BcmonitoringConfigurationProperties.Aws mockAws
	BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
	BcmonitoringConfigurationProperties.Aws.S3 mockS3
	MonitorEventService interactor
	Web3j web3j
	Web3jConfig web3jConfig
	AbiParser mockAbiParser
	ObjectMapper objectMapper
	DownloadAbiService downloadAbiService

	static final String EVENTS_TABLE = "test-events"
	static final String BLOCK_HEIGHT_TABLE = "test-block-height"

	def setupSpec() {
		// Create DynamoDB client for LocalStack
		dynamoDbClient = DynamoDbClient.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.build()

		// Create S3 client for LocalStack
		s3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()

		// Create tables if they don't exist
		AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
		AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
	}

	def cleanupSpec() {
		dynamoDbClient.close()
	}

	def setup() {
		// Set up mocks
		mockLogger = Mock(LoggingService)
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockSubscription = Mock(BcmonitoringConfigurationProperties.Subscription)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
		mockS3 = Mock(BcmonitoringConfigurationProperties.Aws.S3)
		objectMapper = new ObjectMapper()

		// Configure properties
		mockProperties.getSubscription() >> mockSubscription
		mockSubscription.getCheckInterval() >> "100"
		mockSubscription.getAllowableBlockTimestampDiffSec() >> "60"
		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockAws.getS3() >> mockS3
		mockDynamodb.getEventsTableName() >> EVENTS_TABLE
		mockDynamodb.getBlockHeightTableName() >> BLOCK_HEIGHT_TABLE
		mockDynamodb.getTableNameWithPrefix(EVENTS_TABLE) >> EVENTS_TABLE
		mockDynamodb.getTableNameWithPrefix(BLOCK_HEIGHT_TABLE) >> BLOCK_HEIGHT_TABLE
		mockS3.getBucketName() >> "abijson-local-bucket"

		// Create Web3j mock directly in the test
		web3j = Mock(Web3j)

		// Configure the Web3j mock with standard responses
		def blockNumberResponse = new EthBlockNumber()
		blockNumberResponse.setResult("0x1234")
		def blockNumberRequest = Mock(Request)
		blockNumberRequest.send() >> blockNumberResponse
		web3j.ethBlockNumber() >> blockNumberRequest

		// Create Web3jConfig mock
		web3jConfig = Mock(Web3jConfig)
		web3jConfig.getWeb3j() >> web3j


		// Create real AbiParser and S3AbiRepository
		def loggingService = new LoggingService(new BcmonitoringConfigurationProperties())
		def s3AbiRepository = new S3ClientAdaptor(s3Client, loggingService)
		def abiParser = new AbiParser(mockProperties)

		// Create and execute DownloadAbiService to download and parse ABI files
		downloadAbiService = new DownloadAbiService(loggingService, s3AbiRepository, abiParser, mockProperties)


		// Use the real AbiParser for the tests
		mockAbiParser = abiParser

		// Create real repositories
		eventRepo = new EventDao(dynamoDbClient, mockProperties, mockLogger)
		blockHeightRepo = new BlockHeightDao(dynamoDbClient, mockProperties, mockLogger)

		// Create real EthEventLogDao and EventLogRepository
		def ethEventLogDao = new EthEventLogDao(
				mockLogger,
				mockProperties,
				web3jConfig,
				mockAbiParser,
				objectMapper
				)
		eventLogRepo = new EventLogRepositoryImpl(mockLogger, ethEventLogDao)

		// Create the service under test
		interactor = new MonitorEventService(
				mockLogger,
				eventLogRepo,
				eventRepo,
				blockHeightRepo,
				mockProperties,
				web3jConfig,
				ethEventLogDao
				)

		// Clear existing data
		clearTestData()
	}

	def cleanup() {
		// Clean up test data
		clearTestData()
	}

	private void clearTestData() {
		// Delete all items from events table
		def scanResult = dynamoDbClient.scan { it.tableName(EVENTS_TABLE) }
		scanResult.items().each { item ->
			dynamoDbClient.deleteItem { req ->
				req.tableName(EVENTS_TABLE)
				req.key([
					"transactionHash": item.get("transactionHash"),
					"logIndex": item.get("logIndex")
				])
			}
		}

		// Reset block height
		AdhocHelper.saveBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 0L)
	}

	private void setupBlockHeight(long blockHeight) {
		AdhocHelper.saveBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, blockHeight)
	}

	def "should process blockchain events emitted through Web3j"() {
		downloadAbiService.execute()
		given: "A configured block height"
		def blockHeight = 1000L
		setupBlockHeight(blockHeight)

		// Configure Web3j to emit events
		def newBlockNumber = blockHeight + 1
		def timestamp = Instant.now().epochSecond
		def txHash = "0xtest123"
		def contractAddress = "******************************************"

		// Configure ABI parser to return event definition
		def indexedParams = []
		def nonIndexedParams = []
		def abiEvent = new org.web3j.abi.datatypes.Event("Initialized", indexedParams + nonIndexedParams)
		mockAbiParser.getABIEventByLog(_) >> abiEvent

		// Create a block with the specified number and timestamp
		def block = new EthBlock.Block()
		block.number = BigInteger.valueOf(newBlockNumber)
		block.timestamp = BigInteger.valueOf(timestamp)
		block.hash = "0xblock" + newBlockNumber

		// Create transaction objects for the block
		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash
		block.transactions = [txObject]

		// Configure transaction receipt response
		def log = new org.web3j.protocol.core.methods.response.Log()
		log.setTransactionHash(txHash)
		log.setBlockNumber("0x" + Long.toHexString(newBlockNumber))
		log.setLogIndex("0x0")
		log.setTopics([
			"0xbe9b076dc5b65990cca9dd9d7366682482e7817a6f6bc7f4faf4dc32af497f32"
		])
		// Use properly formatted data for a Uint256 parameter (32 bytes)
		log.setData("0x0000000000000000000000000000000000000000000000000000000000000001")
		log.setAddress(contractAddress)

		def receipt = new TransactionReceipt()
		receipt.setTransactionHash(txHash)
		receipt.setBlockNumber("0x" + Long.toHexString(newBlockNumber))
		receipt.setLogs([log])

		def receiptResponse = new EthGetTransactionReceipt()
		receiptResponse.setResult(receipt)

		// Create EthBlock response
		def ethBlock = new EthBlock()
		ethBlock.setResult(block)

		// Configure the mock responses
		def receiptRequest = Mock(Request)
		receiptRequest.send() >> receiptResponse
		web3j.ethGetTransactionReceipt(txHash) >> receiptRequest

		// Configure block flowable to emit this block
		def blockFlowable = Flowable.just(ethBlock)
		web3j.blockFlowable(false) >> blockFlowable

		// Configure eth_getLogs response
		def logResult = new EthLog.LogObject()
		logResult.removed = false
		logResult.logIndex = "0x0"
		logResult.transactionHash = txHash
		logResult.blockNumber = "0x" + Long.toHexString(newBlockNumber)
		logResult.address = contractAddress
		logResult.data = "0x0000000000000000000000000000000000000000000000000000000000000001"
		logResult.topics = [
			"0xbe9b076dc5b65990cca9dd9d7366682482e7817a6f6bc7f4faf4dc32af497f32"
		]

		def ethLog = new EthLog()
		ethLog.setResult([logResult])

		def logsRequest = Mock(Request)
		logsRequest.send() >> ethLog
		web3j.ethGetLogs(_) >> logsRequest

		// Configure block timestamp retrieval
		def blockRequest = Mock(Request)
		blockRequest.send() >> ethBlock
		web3j.ethGetBlockByNumber(_, _) >> blockRequest

		// Access the running field to control the loop
		def runningField = MonitorEventService.class.getDeclaredField("running")
		runningField.setAccessible(true)
		AtomicBoolean running = runningField.get(interactor)

		when: "Starting the service in a separate thread"
		Thread testThread = new Thread({
			interactor.execute()
		})
		testThread.start()

		// Give time for the service to start and process events
		Thread.sleep(1000)

		then: "The service should process the events"
		1 * mockLogger.info("Get blockheight: {}", blockHeight)

		and: "The event should be saved to DynamoDB"
		def eventItem = AdhocHelper.getEventItem(dynamoDbClient, EVENTS_TABLE, txHash, 0)
		eventItem != null
		eventItem.get("name").s() == "Initialized"

		and: "The block height should be updated"
		def blockHeightItem = AdhocHelper.getBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L)
		blockHeightItem != null
		blockHeightItem.get("blockNumber").n() == blockHeight.toString()

		and: "Stop the service"
		running.set(false)
		// Interrupt the thread to ensure it exits any blocking operations
		testThread.interrupt()
		// Wait longer for the thread to terminate
		testThread.join(5000)

		// Force stop if still running
		if (testThread.isAlive()) {
			println "Thread still alive after join, forcing stop"
			testThread.stop()
			Thread.sleep(100)
		}

		!testThread.isAlive()
	}

	def "should handle error scenarios gracefully"() {
		given: "An error scenario configuration"
		def blockHeight = 1000L
		setupBlockHeight(blockHeight)

		// Configure error scenario - block error
		web3j.ethGetBlockByNumber(_, _) >> { throw new IOException("Failed to get block") }

		// Access the running field to control the loop
		def runningField = MonitorEventService.class.getDeclaredField("running")
		runningField.setAccessible(true)
		AtomicBoolean running = runningField.get(interactor)

		when: "Starting the service in a separate thread"
		Thread testThread = new Thread({
			interactor.execute()
		})
		testThread.start()

		// Give time for the service to start and encounter errors
		Thread.sleep(300)

		then: "The service should log errors but continue running"
		1 * mockLogger.info("Get blockheight: {}", blockHeight)

		and: "Stop the service"
		running.set(false)
		testThread.join(1000)
	}
}
